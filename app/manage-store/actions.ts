'use server'

import { revalidatePath } from 'next/cache';
import {
  createProductOffering,
  updateProductOffering,
  updateOfferingStock,
  toggleOfferingAvailability,
  deleteProductOffering,
} from '@/lib/services/product-offerings';
import { CreateOfferingData, UpdateOfferingData } from '@/lib/types/product-offerings';

export interface ActionResult {
  success: boolean;
  error?: string;
  message?: string;
}

export async function createOfferingAction(
  formData: FormData
): Promise<ActionResult> {
  try {
    const productId = formData.get('product_id') as string;
    const stockQuantity = parseInt(formData.get('stock_quantity') as string);
    const minStockLevel = parseInt(formData.get('min_stock_level') as string) || 0;
    const customPrice = formData.get('custom_price') as string;
    const isAvailable = formData.get('is_available') === 'true';
    const isFeatured = formData.get('is_featured') === 'true';
    const creatorNotes = formData.get('creator_notes') as string;
    const displayOrder = parseInt(formData.get('display_order') as string) || 0;

    if (!productId || isNaN(stockQuantity)) {
      return { success: false, error: 'Product and stock quantity are required' };
    }

    const offeringData: CreateOfferingData = {
      product_id: productId,
      stock_quantity: stockQuantity,
      min_stock_level: minStockLevel,
      is_available: isAvailable,
      is_featured: isFeatured,
      custom_price: customPrice ? parseFloat(customPrice) : null,
      creator_notes: creatorNotes || null,
      display_order: displayOrder,
    };

    const result = await createProductOffering(offeringData);

    if (result.error) {
      return { success: false, error: result.error };
    }

    revalidatePath('/manage-store');
    return { success: true, message: 'Product offering created successfully' };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to create offering' 
    };
  }
}

export async function updateOfferingAction(
  offeringId: string,
  formData: FormData
): Promise<ActionResult> {
  try {
    const stockQuantity = parseInt(formData.get('stock_quantity') as string);
    const minStockLevel = parseInt(formData.get('min_stock_level') as string);
    const customPrice = formData.get('custom_price') as string;
    const isAvailable = formData.get('is_available') === 'true';
    const isFeatured = formData.get('is_featured') === 'true';
    const creatorNotes = formData.get('creator_notes') as string;
    const displayOrder = parseInt(formData.get('display_order') as string);

    const offeringData: UpdateOfferingData = {};

    if (!isNaN(stockQuantity)) offeringData.stock_quantity = stockQuantity;
    if (!isNaN(minStockLevel)) offeringData.min_stock_level = minStockLevel;
    if (customPrice !== undefined) {
      offeringData.custom_price = customPrice ? parseFloat(customPrice) : null;
    }
    if (formData.has('is_available')) offeringData.is_available = isAvailable;
    if (formData.has('is_featured')) offeringData.is_featured = isFeatured;
    if (creatorNotes !== undefined) offeringData.creator_notes = creatorNotes || null;
    if (!isNaN(displayOrder)) offeringData.display_order = displayOrder;

    const result = await updateProductOffering(offeringId, offeringData);

    if (result.error) {
      return { success: false, error: result.error };
    }

    revalidatePath('/manage-store');
    return { success: true, message: 'Product offering updated successfully' };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update offering' 
    };
  }
}

export async function updateStockAction(
  offeringId: string,
  newQuantity: number
): Promise<ActionResult> {
  try {
    const result = await updateOfferingStock(offeringId, newQuantity);

    if (result.error) {
      return { success: false, error: result.error };
    }

    revalidatePath('/manage-store');
    return { success: true, message: 'Stock updated successfully' };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update stock' 
    };
  }
}

export async function toggleAvailabilityAction(
  offeringId: string
): Promise<ActionResult> {
  try {
    const result = await toggleOfferingAvailability(offeringId);

    if (result.error) {
      return { success: false, error: result.error };
    }

    revalidatePath('/manage-store');
    return { success: true, message: 'Availability toggled successfully' };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to toggle availability' 
    };
  }
}

export async function deleteOfferingAction(
  offeringId: string
): Promise<ActionResult> {
  try {
    const result = await deleteProductOffering(offeringId);

    if (result.error) {
      return { success: false, error: result.error };
    }

    revalidatePath('/manage-store');
    return { success: true, message: 'Product offering deleted successfully' };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to delete offering' 
    };
  }
}
