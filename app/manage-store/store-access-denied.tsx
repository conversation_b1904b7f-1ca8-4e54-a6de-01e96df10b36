import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert<PERSON>riangle, <PERSON>Lef<PERSON> } from "lucide-react";
import Link from "next/link";
import type { UserType } from "@/lib/types/user-profiles";

interface StoreAccessDeniedProps {
  userType: UserType;
}

export function StoreAccessDenied({ userType }: StoreAccessDeniedProps) {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </Link>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <CardTitle className="text-2xl">Access Restricted</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              This page is only available for creators. Your account is currently set up as a{" "}
              <span className="font-semibold capitalize">{userType}</span>.
            </p>
            
            <div className="bg-muted p-4 rounded-lg text-left">
              <h4 className="font-semibold mb-2">What you can do:</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Browse available products from creators</li>
                <li>• Place orders for your store</li>
                <li>• Manage your store profile and locations</li>
                <li>• Track your order history</li>
              </ul>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg text-left">
              <h4 className="font-semibold mb-2 text-blue-900">Want to become a creator?</h4>
              <p className="text-sm text-blue-700 mb-3">
                Creators can manage their own product inventory, set custom pricing, and sell to stores.
              </p>
              <p className="text-sm text-blue-700">
                Contact support to upgrade your account to a creator account.
              </p>
            </div>

            <div className="flex gap-3 justify-center">
              <Link href="/profile">
                <Button variant="outline">
                  Manage Profile
                </Button>
              </Link>
              <Link href="/stores">
                <Button>
                  Browse Creators
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
