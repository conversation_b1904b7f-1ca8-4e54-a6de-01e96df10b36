import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { getCurrentUserProfile } from "@/lib/services/user-profiles";
import { CreatorDashboard } from "./creator-dashboard";
import { StoreAccessDenied } from "./store-access-denied";

export default async function ManageStorePage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  // Get user profile to check user type
  const profileResult = await getCurrentUserProfile();

  if (!profileResult.success || !profileResult.data) {
    // If no profile exists, redirect to profile creation
    redirect("/profile");
  }

  const userProfile = profileResult.data;

  // Check if user is a creator
  if (userProfile.user_type !== "creator") {
    return <StoreAccessDenied userType={userProfile.user_type} />;
  }

  return <CreatorDashboard userProfile={userProfile} />;
}
