import { Suspense } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Package, AlertTriangle } from "lucide-react";
import { UserProfile } from "@/lib/types/user-profiles";
import { getCurrentUserProductOfferings } from "@/lib/services/product-offerings";
import { ProductOfferingsTable } from "./components/product-offerings-table";
import { AddOfferingDialog } from "./components/add-offering-dialog";
import { DashboardStats } from "./components/dashboard-stats";

interface CreatorDashboardProps {
  userProfile: UserProfile;
}

export async function CreatorDashboard({ userProfile }: CreatorDashboardProps) {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Manage Store</h1>
          <p className="text-muted-foreground">
            Welcome back, {userProfile.store_name}! Manage your product
            offerings and inventory.
          </p>
        </div>
        <AddOfferingDialog>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </AddOfferingDialog>
      </div>

      {/* Dashboard Stats */}
      <Suspense fallback={<DashboardStatsSkeleton />}>
        <DashboardStatsWrapper />
      </Suspense>

      {/* Product Offerings Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Your Product Offerings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<ProductOfferingsTableSkeleton />}>
            <ProductOfferingsTableWrapper />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

// Wrapper component to handle async data fetching for stats
async function DashboardStatsWrapper() {
  const result = await getCurrentUserProductOfferings();

  if (result.error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4" />
            <span>Failed to load dashboard stats: {result.error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return <DashboardStats offerings={result.data || []} />;
}

// Wrapper component to handle async data fetching for table
async function ProductOfferingsTableWrapper() {
  const result = await getCurrentUserProductOfferings();

  if (result.error) {
    return (
      <div className="flex items-center gap-2 text-destructive p-4">
        <AlertTriangle className="h-4 w-4" />
        <span>Failed to load product offerings: {result.error}</span>
      </div>
    );
  }

  return <ProductOfferingsTable offerings={result.data || []} />;
}

// Loading skeletons
function DashboardStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {[...Array(4)].map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
            <div className="h-4 w-4 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent>
            <div className="h-8 w-16 bg-muted animate-pulse rounded mb-1" />
            <div className="h-3 w-24 bg-muted animate-pulse rounded" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function ProductOfferingsTableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="h-10 w-full bg-muted animate-pulse rounded" />
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-16 w-full bg-muted animate-pulse rounded" />
      ))}
    </div>
  );
}
