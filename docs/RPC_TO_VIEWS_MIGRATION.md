# Migration from RPC Functions to Views + RLS

This document explains the migration from RPC functions to RLS-secured database views for better type safety, debugging, and testing.

## Overview

We've replaced most RPC functions with database views combined with Row Level Security (RLS) policies. This approach provides:

1. **Full Type Safety** - Direct queries to views provide compile-time type checking
2. **Easier Debugging** - SQL queries are visible in the code and can be debugged directly
3. **Better Testing** - Service functions can be unit tested without mocking complex RPC calls
4. **Performance** - Views can be optimized and indexed more effectively

## Changes Made

### 1. New Database Views Created

#### `current_user_profile_with_billing`
Replaces: `get_current_user_profile()` RPC function
- Automatically filters to show only the current user's profile
- Includes flattened billing address fields
- Uses `auth.uid()` for security filtering

#### `current_user_shops_with_addresses`
Replaces: `get_current_user_shops()` RPC function
- Shows only shops belonging to the current user
- Includes flattened address fields
- Ordered by creation date

#### `current_user_product_offerings`
Replaces: `get_current_user_product_offerings()` RPC function
- Shows only product offerings for the current user
- Includes calculated fields like stock_status and availability_status
- Properly ordered by featured status, display order, and product name

### 2. Service Functions Updated

#### Before (RPC approach):
```typescript
const { data, error } = await supabase
  .rpc('get_current_user_profile')
  .single();
// ❌ No type safety, hard to debug
```

#### After (Views + RLS approach):
```typescript
const { data, error } = await supabase
  .from('current_user_profile_with_billing')
  .select('*')
  .single();
// ✅ Full type safety, easy to debug, testable
```

### 3. Security-Critical RPC Functions Retained

We kept these RPC functions because they contain important authorization logic:

- `update_offering_stock(offering_id, new_quantity)` - Validates ownership before updating
- `toggle_offering_availability(offering_id)` - Validates ownership before toggling

These functions now have explicit TypeScript types for better type safety.

## Benefits

### Type Safety
- **Before**: RPC calls returned `any` type, no compile-time checking
- **After**: Direct queries provide full TypeScript intellisense and type checking

### Debugging
- **Before**: RPC function logic hidden in database, hard to debug
- **After**: SQL queries visible in code, can be tested directly in database

### Testing
- **Before**: Required complex mocking of RPC functions
- **After**: Can test service functions with simple data mocking

### Performance
- **Before**: RPC functions couldn't be easily optimized
- **After**: Views can be indexed and optimized like regular tables

## Migration Guide

### For Developers

1. **Use the new service functions** - No changes needed in components
2. **Type safety** - You'll now get full TypeScript support when using these functions
3. **Debugging** - You can copy the SQL queries and run them directly in the database

### For Database Changes

1. **Apply the migration**: The migration file `20250814000000_replace_rpc_with_views_and_rls.sql` creates all necessary views
2. **Regenerate types**: Run `npm run gen:types` to get the latest TypeScript types
3. **Test thoroughly**: Ensure all functionality works as expected

## Security Considerations

### RLS Policies
All views inherit security from their underlying tables:
- `user_profiles` - Users can only see their own profile
- `shops` - Users can only see their own shops
- `user_product_offerings` - Creators can only see their own offerings

### Remaining RPC Functions
Security-critical operations still use RPC functions with `SECURITY DEFINER`:
- Stock updates validate ownership before allowing changes
- Availability toggles validate ownership before allowing changes

## Testing

### Unit Tests
Service functions can now be easily unit tested:

```typescript
// Mock the Supabase client response
const mockSupabase = {
  from: jest.fn().mockReturnValue({
    select: jest.fn().mockReturnValue({
      single: jest.fn().mockResolvedValue({
        data: mockUserProfile,
        error: null
      })
    })
  })
};

// Test the service function
const result = await getCurrentUserProfile();
expect(result.success).toBe(true);
```

### Integration Tests
Views can be tested directly in the database:

```sql
-- Test the view returns correct data
SELECT * FROM current_user_profile_with_billing;

-- Test RLS is working
SET ROLE authenticated;
SELECT * FROM current_user_profile_with_billing;
```

## Performance Considerations

### Indexes Added
- `idx_user_profiles_user_id_active` - For fast user profile lookups
- `idx_shops_store_id_active` - For fast shop lookups
- Product offering indexes already exist from previous migration

### Query Optimization
Views can be optimized like regular tables:
- Add indexes on frequently queried columns
- Use EXPLAIN ANALYZE to identify performance bottlenecks
- Consider materialized views for complex calculations if needed

## Rollback Plan

If issues arise, you can temporarily revert by:

1. Updating service functions to use RPC calls again
2. Keeping the views for future migration
3. The RPC functions are still available and functional

## Next Steps

1. **Monitor Performance** - Watch for any performance regressions
2. **Update Documentation** - Update API documentation to reflect new approach
3. **Consider Additional Views** - Identify other RPC functions that could be migrated
4. **Team Training** - Ensure team understands the new patterns
