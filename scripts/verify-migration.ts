/**
 * Verification script for the RPC to Views migration
 * This script checks that our service functions are using the correct approach
 */

import fs from 'fs';
import path from 'path';

interface MigrationCheck {
  file: string;
  function: string;
  expectedApproach: 'view' | 'rpc';
  status: 'pass' | 'fail';
  details: string;
}

function checkServiceFile(filePath: string, checks: Array<{
  functionName: string;
  expectedApproach: 'view' | 'rpc';
  expectedQuery?: string;
}>): MigrationCheck[] {
  const content = fs.readFileSync(filePath, 'utf-8');
  const results: MigrationCheck[] = [];

  for (const check of checks) {
    const functionMatch = content.match(
      new RegExp(`export async function ${check.functionName}[\\s\\S]*?^}`, 'm')
    );

    if (!functionMatch) {
      results.push({
        file: filePath,
        function: check.functionName,
        expectedApproach: check.expectedApproach,
        status: 'fail',
        details: 'Function not found'
      });
      continue;
    }

    const functionBody = functionMatch[0];

    if (check.expectedApproach === 'view') {
      const hasFromCall = functionBody.includes('.from(');
      const hasRpcCall = functionBody.includes('.rpc(');
      
      if (hasFromCall && !hasRpcCall) {
        // Check if it's using the expected view
        if (check.expectedQuery && functionBody.includes(check.expectedQuery)) {
          results.push({
            file: filePath,
            function: check.functionName,
            expectedApproach: check.expectedApproach,
            status: 'pass',
            details: `Using view: ${check.expectedQuery}`
          });
        } else {
          results.push({
            file: filePath,
            function: check.functionName,
            expectedApproach: check.expectedApproach,
            status: 'pass',
            details: 'Using .from() instead of .rpc()'
          });
        }
      } else if (hasRpcCall) {
        results.push({
          file: filePath,
          function: check.functionName,
          expectedApproach: check.expectedApproach,
          status: 'fail',
          details: 'Still using .rpc() instead of view'
        });
      } else {
        results.push({
          file: filePath,
          function: check.functionName,
          expectedApproach: check.expectedApproach,
          status: 'fail',
          details: 'No database query found'
        });
      }
    } else if (check.expectedApproach === 'rpc') {
      const hasRpcCall = functionBody.includes('.rpc(');
      
      if (hasRpcCall) {
        results.push({
          file: filePath,
          function: check.functionName,
          expectedApproach: check.expectedApproach,
          status: 'pass',
          details: 'Correctly using RPC for security-critical operation'
        });
      } else {
        results.push({
          file: filePath,
          function: check.functionName,
          expectedApproach: check.expectedApproach,
          status: 'fail',
          details: 'Should be using RPC for security'
        });
      }
    }
  }

  return results;
}

function main() {
  console.log('🔍 Verifying RPC to Views Migration...\n');

  const checks: MigrationCheck[] = [];

  // Check user-profiles service
  const userProfilesPath = path.join(process.cwd(), 'lib/services/user-profiles.ts');
  checks.push(...checkServiceFile(userProfilesPath, [
    {
      functionName: 'getCurrentUserProfile',
      expectedApproach: 'view',
      expectedQuery: 'current_user_profile_with_billing'
    },
    {
      functionName: 'getCurrentUserShops',
      expectedApproach: 'view',
      expectedQuery: 'current_user_shops_with_addresses'
    }
  ]));

  // Check product-offerings service
  const productOfferingsPath = path.join(process.cwd(), 'lib/services/product-offerings.ts');
  checks.push(...checkServiceFile(productOfferingsPath, [
    {
      functionName: 'getCurrentUserProductOfferings',
      expectedApproach: 'view',
      expectedQuery: 'current_user_product_offerings'
    },
    {
      functionName: 'updateOfferingStock',
      expectedApproach: 'rpc'
    },
    {
      functionName: 'toggleOfferingAvailability',
      expectedApproach: 'rpc'
    }
  ]));

  // Print results
  let passCount = 0;
  let failCount = 0;

  for (const check of checks) {
    const icon = check.status === 'pass' ? '✅' : '❌';
    const fileName = path.basename(check.file);
    
    console.log(`${icon} ${fileName}::${check.function}`);
    console.log(`   Expected: ${check.expectedApproach.toUpperCase()}`);
    console.log(`   Result: ${check.details}\n`);

    if (check.status === 'pass') {
      passCount++;
    } else {
      failCount++;
    }
  }

  // Summary
  console.log('📊 Migration Verification Summary:');
  console.log(`✅ Passed: ${passCount}`);
  console.log(`❌ Failed: ${failCount}`);
  console.log(`📈 Success Rate: ${Math.round((passCount / (passCount + failCount)) * 100)}%\n`);

  if (failCount === 0) {
    console.log('🎉 All checks passed! Migration completed successfully.');
    console.log('\n📋 Benefits achieved:');
    console.log('   • Full TypeScript type safety');
    console.log('   • Easier debugging and testing');
    console.log('   • Better performance with indexed views');
    console.log('   • Security maintained for critical operations');
  } else {
    console.log('⚠️  Some checks failed. Please review the migration.');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
