/**
 * Tests for the migration from RPC functions to Views + RLS
 * These tests verify that the new approach works correctly
 */

import { createClient } from '@supabase/supabase-js';
import {
  getCurrentUserProfile,
  getCurrentUserShops,
} from '../user-profiles';
import {
  getCurrentUserProductOfferings,
  updateOfferingStock,
  toggleOfferingAvailability,
} from '../product-offerings';

// Mock Supabase client
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(),
}));

jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn(),
}));

describe('Views Migration Tests', () => {
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = {
      from: jest.fn(),
      rpc: jest.fn(),
    };

    // Mock both server and client createClient functions
    require('@/lib/supabase/server').createClient.mockResolvedValue(mockSupabase);
    require('@/lib/supabase/client').createClient.mockReturnValue(mockSupabase);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('User Profile Service', () => {
    it('should use view instead of RPC for getCurrentUserProfile', async () => {
      const mockProfile = {
        id: 'profile-id',
        user_id: 'user-id',
        store_name: 'Test Store',
        billing_address_line_1: '123 Main St',
      };

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: mockProfile,
            error: null,
          }),
        }),
      });

      const result = await getCurrentUserProfile();

      // Verify it uses the view, not RPC
      expect(mockSupabase.from).toHaveBeenCalledWith('current_user_profile_with_billing');
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockProfile);
    });

    it('should use view instead of RPC for getCurrentUserShops', async () => {
      const mockShops = [
        {
          id: 'shop-id',
          shop_nickname: 'Main Shop',
          address_line_1: '456 Shop St',
        },
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: mockShops,
            error: null,
          }),
        }),
      });

      const result = await getCurrentUserShops();

      // Verify it uses the view, not RPC
      expect(mockSupabase.from).toHaveBeenCalledWith('current_user_shops_with_addresses');
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockShops);
    });
  });

  describe('Product Offerings Service', () => {
    it('should use view instead of RPC for getCurrentUserProductOfferings', async () => {
      const mockOfferings = [
        {
          offering_id: 'offering-id',
          product_name: 'Test Product',
          stock_quantity: 10,
          is_available: true,
        },
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              order: jest.fn().mockResolvedValue({
                data: mockOfferings,
                error: null,
              }),
            }),
          }),
        }),
      });

      const result = await getCurrentUserProductOfferings();

      // Verify it uses the view, not RPC
      expect(mockSupabase.from).toHaveBeenCalledWith('current_user_product_offerings');
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
      expect(result.data).toEqual(mockOfferings);
    });

    it('should still use RPC for security-critical updateOfferingStock', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: true,
        error: null,
      });

      const result = await updateOfferingStock('offering-id', 5);

      // Verify it still uses RPC for security
      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_offering_stock', {
        offering_id_param: 'offering-id',
        new_quantity: 5,
      });
      expect(mockSupabase.from).not.toHaveBeenCalled();
      expect(result.data).toBe(true);
    });

    it('should still use RPC for security-critical toggleOfferingAvailability', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: true,
        error: null,
      });

      const result = await toggleOfferingAvailability('offering-id');

      // Verify it still uses RPC for security
      expect(mockSupabase.rpc).toHaveBeenCalledWith('toggle_offering_availability', {
        offering_id_param: 'offering-id',
      });
      expect(mockSupabase.from).not.toHaveBeenCalled();
      expect(result.data).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle view query errors gracefully', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Database error' },
          }),
        }),
      });

      const result = await getCurrentUserProfile();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database error');
    });

    it('should handle RPC errors gracefully', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'RPC error' },
      });

      const result = await updateOfferingStock('offering-id', 5);

      expect(result.error).toBe('RPC error');
    });
  });
});
