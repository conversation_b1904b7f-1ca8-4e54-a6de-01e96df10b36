import { createClient } from "@/lib/supabase/client";
import {
  CreateOfferingData,
  GetCurrentUserProductOfferingsResult,
  Product,
  ProductType,
  UpdateOfferingData,
  UserProductOffering,
} from "@/lib/types/product-offerings";
import {
  CurrentUserProductOfferings,
  CurrentUserProductOfferingsResponse,
  ToggleOfferingAvailabilityParams,
  UpdateOfferingStockParams,
} from "@/lib/types/views";

export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export type ProductOfferingResponse = ApiResponse<UserProductOffering>;
export type ProductOfferingsResponse = ApiResponse<
  CurrentUserProductOfferings[]
>;
export type ProductsResponse = ApiResponse<
  (Product & { product_type: ProductType })[]
>;

// Get current user's product offerings
export async function getCurrentUserProductOfferings(): Promise<
  ProductOfferingsResponse
> {
  try {
    const supabase = createClient();

    // Use the new RLS-secured view instead of RPC function
    // This provides full type safety and is easier to debug
    const { data, error } = await supabase
      .from("current_user_product_offerings")
      .select("*")
      .order("is_featured", { ascending: false })
      .order("display_order", { ascending: true })
      .order("product_name", { ascending: true });

    if (error) {
      return { error: error.message };
    }

    return { data: data || [] };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch product offerings",
    };
  }
}

// Get all available products for creating new offerings
export async function getAvailableProducts(): Promise<ProductsResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from("products")
      .select(`
        *,
        product_type:product_types(*)
      `)
      .eq("active", true)
      .order("product_type(name)", { ascending: true });

    if (error) {
      return { error: error.message };
    }

    return { data: data || [] };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to fetch products",
    };
  }
}

// Create a new product offering
export async function createProductOffering(
  offeringData: CreateOfferingData,
): Promise<ProductOfferingResponse> {
  try {
    const supabase = createClient();

    // Get current user profile to get user_profile_id
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { error: "User not authenticated" };
    }

    const { data: profile } = await supabase
      .from("user_profiles")
      .select("id")
      .eq("user_id", user.id)
      .eq("user_type", "creator")
      .single();

    if (!profile) {
      return { error: "Creator profile not found" };
    }

    const { data, error } = await supabase
      .from("user_product_offerings")
      .insert({
        ...offeringData,
        user_profile_id: profile.id,
      })
      .select()
      .single();

    if (error) {
      return { error: error.message };
    }

    return { data };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to create product offering",
    };
  }
}

// Update an existing product offering
export async function updateProductOffering(
  offeringId: string,
  offeringData: UpdateOfferingData,
): Promise<ProductOfferingResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from("user_product_offerings")
      .update(offeringData)
      .eq("id", offeringId)
      .select()
      .single();

    if (error) {
      return { error: error.message };
    }

    return { data };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to update product offering",
    };
  }
}

// Update stock quantity using the database function
// We keep this as RPC because it contains important authorization logic
export async function updateOfferingStock(
  offeringId: string,
  newQuantity: number,
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = createClient();

    // Type-safe RPC call with explicit return type
    const { data, error } = await supabase.rpc("update_offering_stock", {
      offering_id_param: offeringId,
      new_quantity: newQuantity,
    }) as { data: boolean | null; error: any };

    if (error) {
      return { error: error.message };
    }

    if (!data) {
      return {
        error: "Failed to update stock - you may not own this offering",
      };
    }

    return { data: true };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to update stock",
    };
  }
}

// Toggle availability using the database function
// We keep this as RPC because it contains important authorization logic
export async function toggleOfferingAvailability(
  offeringId: string,
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = createClient();

    // Type-safe RPC call with explicit return type
    const { data, error } = await supabase.rpc("toggle_offering_availability", {
      offering_id_param: offeringId,
    }) as { data: boolean | null; error: any };

    if (error) {
      return { error: error.message };
    }

    if (!data) {
      return {
        error: "Failed to toggle availability - you may not own this offering",
      };
    }

    return { data: true };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to toggle availability",
    };
  }
}

// Delete a product offering
export async function deleteProductOffering(
  offeringId: string,
): Promise<ApiResponse<boolean>> {
  try {
    const supabase = createClient();

    const { error } = await supabase
      .from("user_product_offerings")
      .delete()
      .eq("id", offeringId);

    if (error) {
      return { error: error.message };
    }

    return { data: true };
  } catch (error) {
    return {
      error: error instanceof Error
        ? error.message
        : "Failed to delete product offering",
    };
  }
}
