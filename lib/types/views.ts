/**
 * TypeScript types for database views that replace RPC functions
 * These types provide full type safety for the new RLS-secured views
 */

import { Database } from './database';

// Extract view types from the database schema
type DatabaseViews = Database['public']['Views'];

// Current user profile with billing address view
export type CurrentUserProfileWithBilling = {
  id: string;
  user_id: string;
  store_name: string;
  store_logo_url: string | null;
  contact_email: string | null;
  contact_phone: string | null;
  user_type: Database['public']['Enums']['user_type_enum'];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  billing_address_id: string | null;
  store_name_uri: string | null;
  // Flattened billing address fields
  billing_address_nickname: string | null;
  billing_address_line_1: string | null;
  billing_address_line_2: string | null;
  billing_city: string | null;
  billing_state_province: string | null;
  billing_postal_code: string | null;
  billing_country: string | null;
};

// Current user shops with addresses view
export type CurrentUserShopsWithAddresses = {
  id: string;
  store_id: string;
  shop_nickname: string;
  is_active: boolean;
  notes: string | null;
  created_at: string;
  updated_at: string;
  address_id: string | null;
  // Flattened address fields
  address_nickname: string | null;
  address_line_1: string | null;
  address_line_2: string | null;
  city: string | null;
  state_province: string | null;
  postal_code: string | null;
  country: string | null;
};

// Current user product offerings view
export type CurrentUserProductOfferings = {
  offering_id: string;
  product_id: string;
  product_name: string;
  product_description: string | null;
  sku: string;
  color: string | null;
  scent: string | null;
  base_price: number;
  custom_price: number | null;
  effective_price: number;
  stock_quantity: number;
  min_stock_level: number;
  is_available: boolean;
  is_featured: boolean;
  creator_notes: string | null;
  display_order: number | null;
  stock_status: string;
  availability_status: string;
  weight_grams: number;
  offering_created_at: string;
  offering_updated_at: string;
};

// RPC function types for the remaining security-critical functions
export type UpdateOfferingStockParams = {
  offering_id_param: string;
  new_quantity: number;
};

export type ToggleOfferingAvailabilityParams = {
  offering_id_param: string;
};

// These RPC functions return boolean indicating success
export type UpdateOfferingStockResult = boolean;
export type ToggleOfferingAvailabilityResult = boolean;

// Helper types for API responses
export interface ViewApiResponse<T> {
  data?: T;
  error?: string;
}

export type CurrentUserProfileResponse = ViewApiResponse<CurrentUserProfileWithBilling>;
export type CurrentUserShopsResponse = ViewApiResponse<CurrentUserShopsWithAddresses[]>;
export type CurrentUserProductOfferingsResponse = ViewApiResponse<CurrentUserProductOfferings[]>;

// Re-export commonly used types
export type { Database } from './database';
