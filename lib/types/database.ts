export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          extensions?: Json
          variables?: Json
          query?: string
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      addresses: {
        Row: {
          address_line_1: string
          address_line_2: string | null
          city: string
          country: string
          created_at: string | null
          id: string
          nickname: string
          postal_code: string | null
          state_province: string | null
          updated_at: string | null
        }
        Insert: {
          address_line_1: string
          address_line_2?: string | null
          city: string
          country?: string
          created_at?: string | null
          id?: string
          nickname: string
          postal_code?: string | null
          state_province?: string | null
          updated_at?: string | null
        }
        Update: {
          address_line_1?: string
          address_line_2?: string | null
          city?: string
          country?: string
          created_at?: string | null
          id?: string
          nickname?: string
          postal_code?: string | null
          state_province?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      commission_rates: {
        Row: {
          active: boolean | null
          description: string | null
          id: string
          payment_method: Database["public"]["Enums"]["payment_method"] | null
          rate_percentage: number
          sales_channel: Database["public"]["Enums"]["sales_channel"]
        }
        Insert: {
          active?: boolean | null
          description?: string | null
          id?: string
          payment_method?: Database["public"]["Enums"]["payment_method"] | null
          rate_percentage: number
          sales_channel: Database["public"]["Enums"]["sales_channel"]
        }
        Update: {
          active?: boolean | null
          description?: string | null
          id?: string
          payment_method?: Database["public"]["Enums"]["payment_method"] | null
          rate_percentage?: number
          sales_channel?: Database["public"]["Enums"]["sales_channel"]
        }
        Relationships: []
      }
      customers: {
        Row: {
          city: string | null
          commission_percentage: number | null
          company_name: string | null
          country: string | null
          email: string | null
          first_name: string | null
          id: string
          last_name: string | null
          nif: string | null
          phone: string | null
          relationship_started_date: string | null
          street_address: string | null
          type: Database["public"]["Enums"]["customer_type"]
          vat_number: string | null
          zip_code: string | null
        }
        Insert: {
          city?: string | null
          commission_percentage?: number | null
          company_name?: string | null
          country?: string | null
          email?: string | null
          first_name?: string | null
          id?: string
          last_name?: string | null
          nif?: string | null
          phone?: string | null
          relationship_started_date?: string | null
          street_address?: string | null
          type: Database["public"]["Enums"]["customer_type"]
          vat_number?: string | null
          zip_code?: string | null
        }
        Update: {
          city?: string | null
          commission_percentage?: number | null
          company_name?: string | null
          country?: string | null
          email?: string | null
          first_name?: string | null
          id?: string
          last_name?: string | null
          nif?: string | null
          phone?: string | null
          relationship_started_date?: string | null
          street_address?: string | null
          type?: Database["public"]["Enums"]["customer_type"]
          vat_number?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
      invoice_items: {
        Row: {
          details: string | null
          id: string
          invoice_id: string
          line_total: number
          product_id: string
          quantity: number
          unit_price: number
        }
        Insert: {
          details?: string | null
          id?: string
          invoice_id: string
          line_total: number
          product_id: string
          quantity: number
          unit_price: number
        }
        Update: {
          details?: string | null
          id?: string
          invoice_id?: string
          line_total?: number
          product_id?: string
          quantity?: number
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "creator_product_offerings"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "invoice_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "public_product_listings"
            referencedColumns: ["product_id"]
          },
        ]
      }
      invoices: {
        Row: {
          bank_account_iban: string | null
          bank_name: string | null
          bank_swift_bic: string | null
          commission_amount: number | null
          commission_percentage: number | null
          customer_id: string
          due_date: string | null
          id: string
          invoice_date: string
          invoice_number: number
          notes: string | null
          paid_date: string | null
          payment_method: Database["public"]["Enums"]["payment_method"] | null
          payment_reference: string | null
          sales_channel: Database["public"]["Enums"]["sales_channel"]
          shipping_amount: number | null
          status: Database["public"]["Enums"]["invoice_status"] | null
          subtotal: number
          total_amount: number
          vat_amount: number
          vat_percentage: number | null
        }
        Insert: {
          bank_account_iban?: string | null
          bank_name?: string | null
          bank_swift_bic?: string | null
          commission_amount?: number | null
          commission_percentage?: number | null
          customer_id: string
          due_date?: string | null
          id?: string
          invoice_date: string
          invoice_number: number
          notes?: string | null
          paid_date?: string | null
          payment_method?: Database["public"]["Enums"]["payment_method"] | null
          payment_reference?: string | null
          sales_channel: Database["public"]["Enums"]["sales_channel"]
          shipping_amount?: number | null
          status?: Database["public"]["Enums"]["invoice_status"] | null
          subtotal: number
          total_amount: number
          vat_amount: number
          vat_percentage?: number | null
        }
        Update: {
          bank_account_iban?: string | null
          bank_name?: string | null
          bank_swift_bic?: string | null
          commission_amount?: number | null
          commission_percentage?: number | null
          customer_id?: string
          due_date?: string | null
          id?: string
          invoice_date?: string
          invoice_number?: number
          notes?: string | null
          paid_date?: string | null
          payment_method?: Database["public"]["Enums"]["payment_method"] | null
          payment_reference?: string | null
          sales_channel?: Database["public"]["Enums"]["sales_channel"]
          shipping_amount?: number | null
          status?: Database["public"]["Enums"]["invoice_status"] | null
          subtotal?: number
          total_amount?: number
          vat_amount?: number
          vat_percentage?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      product_types: {
        Row: {
          description: string | null
          id: string
          name: string
          price: number
          weight_grams: number | null
        }
        Insert: {
          description?: string | null
          id?: string
          name: string
          price: number
          weight_grams?: number | null
        }
        Update: {
          description?: string | null
          id?: string
          name?: string
          price?: number
          weight_grams?: number | null
        }
        Relationships: []
      }
      products: {
        Row: {
          active: boolean | null
          color: string | null
          id: string
          product_type_id: string
          scent: string | null
          sku: string | null
        }
        Insert: {
          active?: boolean | null
          color?: string | null
          id?: string
          product_type_id: string
          scent?: string | null
          sku?: string | null
        }
        Update: {
          active?: boolean | null
          color?: string | null
          id?: string
          product_type_id?: string
          scent?: string | null
          sku?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "products_product_type_id_fkey"
            columns: ["product_type_id"]
            isOneToOne: false
            referencedRelation: "creator_product_offerings"
            referencedColumns: ["product_type_id"]
          },
          {
            foreignKeyName: "products_product_type_id_fkey"
            columns: ["product_type_id"]
            isOneToOne: false
            referencedRelation: "product_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_product_type_id_fkey"
            columns: ["product_type_id"]
            isOneToOne: false
            referencedRelation: "public_product_listings"
            referencedColumns: ["product_type_id"]
          },
        ]
      }
      shops: {
        Row: {
          address_id: string | null
          created_at: string | null
          id: string
          is_active: boolean
          notes: string | null
          shop_nickname: string
          store_id: string
          updated_at: string | null
        }
        Insert: {
          address_id?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean
          notes?: string | null
          shop_nickname: string
          store_id: string
          updated_at?: string | null
        }
        Update: {
          address_id?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean
          notes?: string | null
          shop_nickname?: string
          store_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "shops_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "shops_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "shops_with_addresses"
            referencedColumns: ["address_id"]
          },
          {
            foreignKeyName: "shops_address_id_fkey"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "user_profiles_with_billing"
            referencedColumns: ["billing_address_id"]
          },
          {
            foreignKeyName: "shops_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "creator_product_offerings"
            referencedColumns: ["creator_id"]
          },
          {
            foreignKeyName: "shops_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "public_product_listings"
            referencedColumns: ["creator_id"]
          },
          {
            foreignKeyName: "shops_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "shops_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "user_profiles_with_billing"
            referencedColumns: ["id"]
          },
        ]
      }
      user_product_offerings: {
        Row: {
          created_at: string | null
          creator_notes: string | null
          custom_price: number | null
          display_order: number | null
          id: string
          is_available: boolean
          is_featured: boolean
          min_stock_level: number
          product_id: string
          stock_quantity: number
          updated_at: string | null
          user_profile_id: string
        }
        Insert: {
          created_at?: string | null
          creator_notes?: string | null
          custom_price?: number | null
          display_order?: number | null
          id?: string
          is_available?: boolean
          is_featured?: boolean
          min_stock_level?: number
          product_id: string
          stock_quantity?: number
          updated_at?: string | null
          user_profile_id: string
        }
        Update: {
          created_at?: string | null
          creator_notes?: string | null
          custom_price?: number | null
          display_order?: number | null
          id?: string
          is_available?: boolean
          is_featured?: boolean
          min_stock_level?: number
          product_id?: string
          stock_quantity?: number
          updated_at?: string | null
          user_profile_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_product_offerings_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "creator_product_offerings"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "user_product_offerings_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_product_offerings_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "public_product_listings"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "user_product_offerings_user_profile_id_fkey"
            columns: ["user_profile_id"]
            isOneToOne: false
            referencedRelation: "creator_product_offerings"
            referencedColumns: ["creator_id"]
          },
          {
            foreignKeyName: "user_product_offerings_user_profile_id_fkey"
            columns: ["user_profile_id"]
            isOneToOne: false
            referencedRelation: "public_product_listings"
            referencedColumns: ["creator_id"]
          },
          {
            foreignKeyName: "user_product_offerings_user_profile_id_fkey"
            columns: ["user_profile_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_product_offerings_user_profile_id_fkey"
            columns: ["user_profile_id"]
            isOneToOne: false
            referencedRelation: "user_profiles_with_billing"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          billing_address_id: string | null
          contact_email: string | null
          contact_phone: string | null
          created_at: string | null
          id: string
          is_active: boolean
          store_logo_url: string | null
          store_name: string
          store_name_uri: string | null
          updated_at: string | null
          user_id: string
          user_type: Database["public"]["Enums"]["user_type_enum"]
        }
        Insert: {
          billing_address_id?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean
          store_logo_url?: string | null
          store_name: string
          store_name_uri?: string | null
          updated_at?: string | null
          user_id: string
          user_type?: Database["public"]["Enums"]["user_type_enum"]
        }
        Update: {
          billing_address_id?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean
          store_logo_url?: string | null
          store_name?: string
          store_name_uri?: string | null
          updated_at?: string | null
          user_id?: string
          user_type?: Database["public"]["Enums"]["user_type_enum"]
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_billing_address_id_fkey"
            columns: ["billing_address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_profiles_billing_address_id_fkey"
            columns: ["billing_address_id"]
            isOneToOne: false
            referencedRelation: "shops_with_addresses"
            referencedColumns: ["address_id"]
          },
          {
            foreignKeyName: "user_profiles_billing_address_id_fkey"
            columns: ["billing_address_id"]
            isOneToOne: false
            referencedRelation: "user_profiles_with_billing"
            referencedColumns: ["billing_address_id"]
          },
        ]
      }
    }
    Views: {
      creator_product_offerings: {
        Row: {
          availability_status: string | null
          base_price: number | null
          color: string | null
          creator_id: string | null
          creator_logo_url: string | null
          creator_name: string | null
          creator_notes: string | null
          creator_user_id: string | null
          custom_price: number | null
          display_order: number | null
          effective_price: number | null
          is_available: boolean | null
          is_featured: boolean | null
          min_stock_level: number | null
          offering_created_at: string | null
          offering_id: string | null
          offering_updated_at: string | null
          product_active: boolean | null
          product_description: string | null
          product_id: string | null
          product_name: string | null
          product_type_id: string | null
          scent: string | null
          sku: string | null
          stock_quantity: number | null
          stock_status: string | null
          weight_grams: number | null
        }
        Relationships: []
      }
      public_product_listings: {
        Row: {
          availability_status: string | null
          base_price: number | null
          color: string | null
          creator_contact_email: string | null
          creator_id: string | null
          creator_logo_url: string | null
          creator_name: string | null
          custom_price: number | null
          display_order: number | null
          effective_price: number | null
          is_available: boolean | null
          is_featured: boolean | null
          min_stock_level: number | null
          offering_created_at: string | null
          offering_id: string | null
          offering_updated_at: string | null
          product_active: boolean | null
          product_description: string | null
          product_id: string | null
          product_name: string | null
          product_type_id: string | null
          scent: string | null
          sku: string | null
          stock_quantity: number | null
          stock_status: string | null
          weight_grams: number | null
        }
        Relationships: []
      }
      shops_with_addresses: {
        Row: {
          address_id: string | null
          address_line_1: string | null
          address_line_2: string | null
          address_nickname: string | null
          city: string | null
          country: string | null
          created_at: string | null
          id: string | null
          is_active: boolean | null
          notes: string | null
          postal_code: string | null
          shop_nickname: string | null
          state_province: string | null
          store_id: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "shops_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "creator_product_offerings"
            referencedColumns: ["creator_id"]
          },
          {
            foreignKeyName: "shops_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "public_product_listings"
            referencedColumns: ["creator_id"]
          },
          {
            foreignKeyName: "shops_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "shops_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "user_profiles_with_billing"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles_with_billing: {
        Row: {
          billing_address_id: string | null
          billing_address_line_1: string | null
          billing_address_line_2: string | null
          billing_address_nickname: string | null
          billing_city: string | null
          billing_country: string | null
          billing_postal_code: string | null
          billing_state_province: string | null
          contact_email: string | null
          contact_phone: string | null
          created_at: string | null
          id: string | null
          is_active: boolean | null
          store_logo_url: string | null
          store_name: string | null
          updated_at: string | null
          user_id: string | null
          user_type: Database["public"]["Enums"]["user_type_enum"] | null
        }
        Relationships: []
      }
    }
    Functions: {
      check_product_availability: {
        Args: { creator_id_param: string; product_id_param: string }
        Returns: {
          stock_quantity: number
          is_available: boolean
          effective_price: number
          stock_status: string
        }[]
      }
      get_current_user_product_offerings: {
        Args: Record<PropertyKey, never>
        Returns: {
          stock_status: string
          availability_status: string
          weight_grams: number
          effective_price: number
          min_stock_level: number
          custom_price: number
          base_price: number
          offering_id: string
          product_id: string
          scent: string
          product_name: string
          product_description: string
          sku: string
          color: string
          stock_quantity: number
          is_available: boolean
          is_featured: boolean
          creator_notes: string
          display_order: number
        }[]
      }
      get_current_user_profile: {
        Args: Record<PropertyKey, never>
        Returns: {
          billing_address_id: string
          id: string
          user_id: string
          store_name: string
          store_logo_url: string
          contact_email: string
          contact_phone: string
          user_type: Database["public"]["Enums"]["user_type_enum"]
          is_active: boolean
          created_at: string
          updated_at: string
          billing_address_nickname: string
          billing_address_line_1: string
          billing_address_line_2: string
          billing_city: string
          billing_state_province: string
          billing_postal_code: string
          billing_country: string
        }[]
      }
      get_current_user_shops: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          created_at: string
          address_id: string
          address_nickname: string
          address_line_1: string
          address_line_2: string
          city: string
          state_province: string
          postal_code: string
          country: string
          updated_at: string
          notes: string
          is_active: boolean
          shop_nickname: string
          store_id: string
        }[]
      }
      get_featured_products: {
        Args: { limit_param?: number }
        Returns: {
          stock_status: string
          color: string
          scent: string
          effective_price: number
          weight_grams: number
          offering_id: string
          creator_name: string
          creator_logo_url: string
          product_name: string
          product_description: string
        }[]
      }
      toggle_offering_availability: {
        Args: { offering_id_param: string }
        Returns: boolean
      }
      update_offering_stock: {
        Args: { new_quantity: number; offering_id_param: string }
        Returns: boolean
      }
    }
    Enums: {
      customer_type: "individual" | "company"
      invoice_status: "draft" | "sent" | "paid" | "overdue" | "cancelled"
      payment_method: "cash" | "card" | "bank_transfer" | "shopify"
      sales_channel: "in_store" | "website" | "partner_store"
      user_type_enum: "creator" | "store"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      customer_type: ["individual", "company"],
      invoice_status: ["draft", "sent", "paid", "overdue", "cancelled"],
      payment_method: ["cash", "card", "bank_transfer", "shopify"],
      sales_channel: ["in_store", "website", "partner_store"],
      user_type_enum: ["creator", "store"],
    },
  },
} as const

