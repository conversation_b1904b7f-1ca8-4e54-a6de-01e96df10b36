{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "gen:types": "supabase gen types --lang=typescript --local> lib/types/database.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "test:e2e": "playwright test", "test:summary": "node scripts/test-summary.js", "test:verify": "node scripts/test-summary.js --verify"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "latest", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.42.1", "@testing-library/dom": "^10.4.1", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.1", "msw": "^2.2.1", "postcss": "^8", "supabase": "^2.31.8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}